import { Icon } from "@iconify/react/dist/iconify.js";
import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import axios from "axios";

import logoImg from "../assets/images/logo.png";
import authImg from "../assets/images/auth/auth-img.png";

const SignUpLayer = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    repassword: "",
    firstname: "",
    lastname: "",
    phone: "",
    email: "",
    company_name: "",
    tax_number: "",
    address: "",
  });
  const [errorMsg, setErrorMsg] = useState("");
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const validateForm = () => {
    const {
      username,
      password,
      repassword,
      firstname,
      lastname,
      phone,
      email,
    } = formData;

    if (
      !username ||
      !password ||
      !repassword ||
      !firstname ||
      !lastname ||
      !phone ||
      !email
    ) {
      return "Vui lòng điền đầy đủ tất cả các trường bắt buộc.";
    }

    if (password.length < 6) {
      return "Mật khẩu phải có ít nhất 6 ký tự.";
    }

    if (password !== repassword) {
      return "Mật khẩu và Nhập lại mật khẩu không khớp.";
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return "Email không đúng định dạng.";
    }

    const phoneRegex = /^[0-9]{9,}$/;
    if (!phoneRegex.test(phone)) {
      return "Số điện thoại không hợp lệ. Phải có ít nhất 9 chữ số.";
    }

    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMsg("");

    const validationError = validateForm();
    if (validationError) {
      setErrorMsg(validationError);
      return;
    }

    setLoading(true);

    try {
      const payload = new URLSearchParams();
      payload.append("action", "register");
      Object.entries(formData).forEach(([key, value]) =>
        payload.append(key, value)
      );

      const res = await axios.post(
        "https://api.pay2s.vn/api/v1/auth",
        payload,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      if (res.data.status) {
        navigate("/sign-in");
      } else {
        setErrorMsg(res.data.message || "Đăng ký không thành công.");
      }
    } catch (err) {
      console.error(err);
      setErrorMsg("Lỗi hệ thống hoặc không thể kết nối máy chủ.");
    }

    setLoading(false);
  };

  return (
    <section className="auth bg-base d-flex flex-wrap">
      <div className="auth-left d-lg-block d-none">
        <div className="d-flex align-items-center flex-column h-100 justify-content-center">
          <img src={authImg} alt="Đăng ký Pay2S" />
        </div>
      </div>
      <div className="auth-right py-32 px-24 d-flex flex-column justify-content-center">
        <div className="max-w-464-px mx-auto w-100">
          <div>
            <Link to="/sign-in" className="mb-40 max-w-290-px d-inline-block">
              <img src={logoImg} alt="Pay2S Logo" />
            </Link>
            <h4 className="mb-12">Tạo tài khoản mới</h4>
            <p className="mb-32 text-secondary-light text-lg">
              Vui lòng điền đầy đủ thông tin bên dưới
            </p>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="icon-field mb-16 d-flex gap-2">
              <input
                type="text"
                name="firstname"
                className="form-control"
                placeholder="Họ *"
                value={formData.firstname}
                onChange={handleChange}
              />
              <input
                type="text"
                name="lastname"
                className="form-control"
                placeholder="Tên *"
                value={formData.lastname}
                onChange={handleChange}
              />
            </div>
            <div className="icon-field mb-16 d-flex gap-2">
              <input
                type="text"
                name="username"
                className="form-control"
                placeholder="Tên đăng nhập *"
                value={formData.username}
                onChange={handleChange}
              />
              <input
                type="text"
                name="phone"
                className="form-control"
                placeholder="Số điện thoại *"
                value={formData.phone}
                onChange={handleChange}
              />
            </div>

            <div className="icon-field mb-16 d-flex gap-2">
              <input
                type="password"
                name="password"
                className="form-control"
                placeholder="Mật khẩu *"
                value={formData.password}
                onChange={handleChange}
              />

              <input
                type="password"
                name="repassword"
                className="form-control"
                placeholder="Nhập lại mật khẩu *"
                value={formData.repassword}
                onChange={handleChange}
              />
            </div>
            <div className="icon-field mb-16">
              <input
                type="email"
                name="email"
                className="form-control"
                placeholder="Email *"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            <div className="icon-field mb-16">
              <input
                type="text"
                name="company_name"
                className="form-control"
                placeholder="Tên công ty"
                value={formData.company_name}
                onChange={handleChange}
              />
            </div>
            <div className="icon-field mb-16">
              <input
                type="text"
                name="tax_number"
                className="form-control"
                placeholder="Mã số thuế"
                value={formData.tax_number}
                onChange={handleChange}
              />
            </div>
            <div className="icon-field mb-16">
              <input
                type="text"
                name="address"
                className="form-control"
                placeholder="Địa chỉ"
                value={formData.address}
                onChange={handleChange}
              />
            </div>

            {errorMsg && (
              <div className="alert alert-danger py-2 px-3 mt-2">
                {errorMsg}
              </div>
            )}

            <button
              type="submit"
              className="btn btn-primary w-100"
              disabled={loading}
            >
              {loading ? "Đang đăng ký..." : "Đăng ký"}
            </button>

            <div className="mt-32 text-center text-sm">
              <p className="mb-0">
                Đã có tài khoản?{" "}
                <Link to="/sign-in" className="text-primary-600 fw-semibold">
                  Đăng nhập
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default SignUpLayer;
