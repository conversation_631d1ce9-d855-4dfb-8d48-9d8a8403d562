import React, { useEffect, useState, useMemo } from "react";
import { useLocation } from "react-router-dom"; // FIX: Thêm useLocation
import * as XLSX from "xlsx";
import { Icon } from "@iconify/react/dist/iconify.js";
import useBankApi from "../../callapi/Bank.jsx";

// Import hình ảnh logo ngân hàng
import bgAcb from "../../assets/images/banks/acb.jpg";
import bgBidv from "../../assets/images/banks/bidv.jpg";
import bgMbb from "../../assets/images/banks/mbb.jpg";
import bgMomo from "../../assets/images/banks/momo.jpg";
import bgSeab from "../../assets/images/banks/seab.jpg";
import bgTcb from "../../assets/images/banks/tcb.jpg";
import bgTpb from "../../assets/images/banks/tpb.jpg";
import bgVcb from "../../assets/images/banks/vcb.jpg";
import bgVtb from "../../assets/images/banks/vtb.jpg";

// Component chính
const TransactionBank = () => {
  const [transactions, setTransactions] = useState([]);
  const {
    data: apiData,
    loading,
    error,
    callApi: getBankTransactions,
  } = useBankApi();

  const [accountFilter, setAccountFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  const location = useLocation(); // FIX: Lấy location

  const bankLogoMap = {
    ACB: bgAcb,
    BIDV: bgBidv,
    MBB: bgMbb,
    MOMO: bgMomo,
    SEAB: bgSeab,
    TCB: bgTcb,
    TPB: bgTpb,
    VCB: bgVcb,
    VTB: bgVtb,
  };
  const getBankLogo = (shortName) => bankLogoMap[shortName] || null;
  const typeMap = { IN: "Tiền vào", OUT: "Tiền ra" };

  const fetchAndSetTransactions = React.useCallback(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      getBankTransactions({ action: "list_transactions", user_id: userId });
    }
  }, [getBankTransactions]);

  useEffect(() => {
    fetchAndSetTransactions();
  }, [fetchAndSetTransactions]);

  // FIX: useEffect để nhận STK từ URL và tự động filter
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const accountNumberFromUrl = params.get("accountNumber");
    if (accountNumberFromUrl) {
      setAccountFilter(accountNumberFromUrl);
    }
  }, [location.search]);

  useEffect(() => {
    if (
      apiData &&
      apiData.transactions &&
      Array.isArray(apiData.transactions)
    ) {
      setTransactions(apiData.transactions);
    }
  }, [apiData]);

  const filteredData = useMemo(() => {
    return transactions.filter((trans) => {
      const matchAccount =
        !accountFilter || trans.accountNumber === accountFilter;
      const matchType = !typeFilter || trans.type === typeFilter;
      const transDate = new Date(trans.transactionDate.replace(" ", "T"));
      const fromDate = dateFrom ? new Date(dateFrom) : null;
      const toDate = dateTo ? new Date(dateTo) : null;
      if (fromDate) fromDate.setHours(0, 0, 0, 0);
      if (toDate) toDate.setHours(23, 59, 59, 999);
      const matchDate =
        (!fromDate || transDate >= fromDate) &&
        (!toDate || transDate <= toDate);
      const term = searchTerm.toLowerCase();
      const matchSearch =
        !term ||
        String(trans.id).toLowerCase().includes(term) ||
        trans.description.toLowerCase().includes(term);
      return matchAccount && matchType && matchSearch && matchDate;
    });
  }, [transactions, accountFilter, typeFilter, dateFrom, dateTo, searchTerm]);

  const summaryData = useMemo(() => {
    const incoming = filteredData.filter((t) => t.type === "IN");
    const outgoing = filteredData.filter((t) => t.type === "OUT");
    const totalIncoming = incoming.reduce((sum, t) => sum + t.amount, 0);
    const totalOutgoing = outgoing.reduce((sum, t) => sum + t.amount, 0);
    return {
      total: {
        amount: totalIncoming + totalOutgoing,
        count: filteredData.length,
      },
      incoming: { amount: totalIncoming, count: incoming.length },
      outgoing: { amount: totalOutgoing, count: outgoing.length },
    };
  }, [filteredData]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredData.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredData, currentPage, itemsPerPage]);
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  const formatCurrency = (amount, type = null) => {
    const amountAbs = Math.abs(amount || 0);
    const formatted = new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amountAbs);
    if (type === "IN") return `+ ${formatted}`;
    if (type === "OUT") return `- ${formatted}`;
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount || 0);
  };

  const handleResetFilters = () => {
    setAccountFilter("");
    setTypeFilter("");
    setDateFrom("");
    setDateTo("");
    setCurrentPage(1);
    setSearchTerm("");
  };

  const exportExcel = () => {
    // 1. Kiểm tra nếu không có dữ liệu để xuất
    if (filteredData.length === 0) {
      alert("Không có dữ liệu giao dịch để xuất file Excel.");
      return;
    }

    // 2. Chuẩn bị dữ liệu với tiêu đề tiếng Việt tường minh
    const dataToExport = filteredData.map((trans, index) => ({
      STT: index + 1,
      "ID Giao Dịch": String(trans.id), // Đảm bảo ID là dạng chuỗi
      "Ngân Hàng": trans.shortBankName,
      "Số Tài Khoản": trans.accountNumber,
      "Số Tiền (VND)": trans.type === "IN" ? trans.amount : -trans.amount, // Tiền ra sẽ là số âm
      "Loại Giao Dịch": typeMap[trans.type], // "Tiền vào" hoặc "Tiền ra"
      "Thời Gian Giao Dịch": trans.transactionDate,
      "Nội Dung Giao Dịch": trans.description,
      "Hóa Đơn Liên Quan": trans.invoiceRelated || "-",
    }));

    // 3. Tạo một worksheet (trang tính) từ dữ liệu đã chuẩn bị
    const ws = XLSX.utils.json_to_sheet(dataToExport);

    // (Tùy chọn) Tùy chỉnh độ rộng cho các cột để file đẹp hơn
    ws["!cols"] = [
      { wch: 5 }, // STT
      { wch: 25 }, // ID Giao Dịch
      { wch: 12 }, // Ngân Hàng
      { wch: 20 }, // Số Tài Khoản
      { wch: 18 }, // Số Tiền (VND)
      { wch: 15 }, // Loại Giao Dịch
      { wch: 20 }, // Thời Gian Giao Dịch
      { wch: 50 }, // Nội Dung Giao Dịch
      { wch: 20 }, // Hóa Đơn Liên Quan
    ];

    // Thêm định dạng tiền tệ cho cột 'Số Tiền (VND)'
    // Ký hiệu '#' đại diện cho một chữ số, ',' là dấu phân cách hàng nghìn.
    const moneyFormat = '#,##0 "VND"';
    // Duyệt qua tất cả các ô trong cột 'Số Tiền' (cột E) và áp dụng định dạng
    for (let i = 2; i <= dataToExport.length + 1; i++) {
      // Bắt đầu từ hàng 2 (vì hàng 1 là tiêu đề)
      const cellRef = XLSX.utils.encode_cell({ c: 4, r: i - 1 }); // Cột E là cột thứ 4 (0-indexed)
      if (ws[cellRef]) {
        // Kiểm tra ô có tồn tại
        ws[cellRef].z = moneyFormat;
      }
    }

    // 4. Tạo một workbook (sổ làm việc) mới
    const wb = XLSX.utils.book_new();

    // 5. Thêm worksheet vào workbook với tên là "Lich Su Giao Dich"
    XLSX.utils.book_append_sheet(wb, ws, "Lich Su Giao Dich");

    // 6. Tạo tên file động và tải về máy người dùng
    const today = new Date();
    const fileName = `Lich Su Giao Dich ${today.getDate()}-${
      today.getMonth() + 1
    }-${today.getFullYear()}.xlsx`;
    XLSX.writeFile(wb, fileName);
  };
  const handlePrint = () => {
    window.print();
  };

  const Pagination = () => {
    if (totalPages <= 1) return null;
    const pageNumbers = [];
    const maxPagesToShow = 5;
    if (totalPages <= maxPagesToShow + 2) {
      for (let i = 1; i <= totalPages; i++) pageNumbers.push(i);
    } else {
      pageNumbers.push(1);
      if (currentPage > 3) pageNumbers.push("...");
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);
      if (currentPage <= 2) end = 3;
      if (currentPage >= totalPages - 1) start = totalPages - 2;
      for (let i = start; i <= end; i++) pageNumbers.push(i);
      if (currentPage < totalPages - 2) pageNumbers.push("...");
      pageNumbers.push(totalPages);
    }

    return (
      <nav aria-label="pagination">
        <ul className="pagination mb-0">
          <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
            <button
              className="page-link"
              onClick={() => setCurrentPage((c) => c - 1)}
            >
              &laquo;
            </button>
          </li>
          {pageNumbers.map((num, index) =>
            num === "..." ? (
              <li key={`ellipsis-${index}`} className="page-item disabled">
                <span className="page-link">...</span>
              </li>
            ) : (
              <li
                key={num}
                className={`page-item ${currentPage === num ? "active" : ""}`}
              >
                <button
                  className="page-link"
                  onClick={() => setCurrentPage(num)}
                >
                  {num}
                </button>
              </li>
            )
          )}
          <li
            className={`page-item ${
              currentPage === totalPages ? "disabled" : ""
            }`}
          >
            <button
              className="page-link"
              onClick={() => setCurrentPage((c) => c + 1)}
            >
              &raquo;
            </button>
          </li>
        </ul>
      </nav>
    );
  };

  return (
    <>
      <div className="container-fluid mt-4">
        <div className="card">
          <div className="card-body">
            <div className="row g-3 align-items-center">
              <div className="col-md-3">
                <select
                  className="form-select"
                  value={accountFilter}
                  onChange={(e) => setAccountFilter(e.target.value)}
                >
                  <option value="">Tất cả tài khoản</option>
                  {[...new Set(transactions.map((t) => t.accountNumber))]
                    .filter(Boolean)
                    .map((acc) => (
                      <option key={acc} value={acc}>
                        {acc}
                      </option>
                    ))}
                </select>
              </div>
              <div className="col-md-2">
                <select
                  className="form-select"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <option value="">Tất cả loại</option>
                  {Object.entries(typeMap).map(([key, value]) => (
                    <option key={key} value={key}>
                      {value}
                    </option>
                  ))}
                </select>
              </div>
              <div className="col-md-4">
                <div className="input-group">
                  <input
                    type="date"
                    className="form-control"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                  />
                  <span className="input-group-text">
                    <Icon icon="iconoir:arrow-right" />
                  </span>
                  <input
                    type="date"
                    className="form-control"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                  />
                </div>
              </div>
              <div className="col-md-3">
                <button
                  className="btn btn-primary w-100"
                  onClick={handleResetFilters}
                >
                  Chọn lại
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="row g-3 mt-1">
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#dcfce7", color: "#166534" }}
              >
                Tổng tiền vào
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fw-bold mb-0 text-success">
                      {formatCurrency(summaryData.incoming.amount, "IN")}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Số lượng</small>
                    <p className="fw-bold mb-0">{summaryData.incoming.count}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#fee2e2", color: "#991b1b" }}
              >
                Tổng tiền ra
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fw-bold mb-0 text-danger">
                      {formatCurrency(summaryData.outgoing.amount, "OUT")}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Số lượng</small>
                    <p className="fw-bold mb-0">{summaryData.outgoing.count}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#eef2ff", color: "#3730a3" }}
              >
                Tổng tiền giao dịch
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fw-bold mb-0">
                      {formatCurrency(summaryData.total.amount)}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Số lượng</small>
                    <p className="fw-bold mb-0">{summaryData.total.count}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Table Section */}
        <div className="card mt-3 printable-area">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <input
                type="text"
                className="form-control"
                placeholder="Tìm kiếm ID, nội dung..."
                style={{ maxWidth: "300px" }}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div>
                <button
                  className="btn btn-outline-success ms-2"
                  onClick={exportExcel}
                >
                  <Icon icon="mdi:file-excel-outline" /> Excel
                </button>
                <button
                  className="btn btn-outline-secondary ms-2"
                  onClick={handlePrint}
                >
                  <Icon icon="mdi:printer-outline" /> Print
                </button>
              </div>
            </div>

            {loading ? (
              <p className="text-center p-5">Đang tải...</p>
            ) : error ? (
              <div className="alert alert-danger">{error}</div>
            ) : (
              <div className="table-responsive">
                <table className="table basic-border-table mb-0">
                  <thead className="table-light">
                    <tr>
                      <th>ID</th>
                      <th>Tài khoản</th>
                      <th>Số tiền</th>
                      <th>Loại</th>
                      <th>Thời gian</th>
                      <th>Nội dung</th>
                      <th>Hóa đơn liên quan</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.map((trans) => {
                      const logoSrc = getBankLogo(trans.shortBankName);
                      return (
                        <tr key={trans.id}>
                          <td
                            className="text-muted"
                            style={{
                              fontSize: "80%",
                              wordBreak: "break-word",
                            }}
                          >
                            {trans.id}
                          </td>

                          <td>
                            <div className="d-flex flex-column align-items-center">
                              <img
                                src={logoSrc}
                                alt={trans.bankName}
                                style={{ height: "40px" }}
                              />
                              <small className="text-muted">
                                {trans.accountNumber}
                              </small>
                            </div>
                          </td>
                          <td
                            className={`fw-bold ${
                              trans.type === "IN"
                                ? "text-success"
                                : "text-danger"
                            }`}
                          >
                            {formatCurrency(trans.amount, trans.type)}
                          </td>
                          <td>
                            <span
                              className={`badge ${
                                trans.type === "IN"
                                  ? "bg-success-focus text-success-main"
                                  : "bg-danger-focus text-danger-main"
                              }`}
                            >
                              {typeMap[trans.type]}
                            </span>
                          </td>
                          <td>{trans.transactionDate}</td>

                          <td
                            style={{
                              minWidth: "250px",
                              maxWidth: "400px",
                              whiteSpace: "normal",
                              wordBreak: "break-word",
                            }}
                          >
                            {trans.description}
                          </td>
                          <td>{trans.invoiceRelated || "-"}</td>
                        </tr>
                      );
                    })}
                    {paginatedData.length === 0 && (
                      <tr>
                        <td colSpan="7" className="text-center py-4">
                          Không có giao dịch nào.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {!loading && !error && totalPages > 1 && (
              <div className="d-flex justify-content-between align-items-center mt-3">
                <span>
                  Hiển thị {paginatedData.length} trên tổng số{" "}
                  {filteredData.length} giao dịch
                </span>
                <Pagination />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default TransactionBank;
