import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom"; // Link đã có sẵn
import useBankApi from "../callapi/Bank.jsx";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import useBootstrapTooltip from "../hook/useBootstrapTooltip.js";
// === COMPONENT MODAL XÁC NHẬN XÓA (Không thay đổi) ===
const ConfirmDeleteModal = ({
  isOpen,
  onClose,
  onConfirm,
  accountName,
  loading,
}) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1050,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: "8px",
          width: "90%",
          maxWidth: "400px",
          boxShadow: "0 5px 15px rgba(0,0,0,.5)",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{ padding: "2rem", textAlign: "center" }}>
          <Icon
            icon="ph:warning-circle-bold"
            className="text-danger mb-3"
            style={{ fontSize: "4rem" }}
          />
          <h5 className="fw-bold">Xác nhận xóa</h5>
          <p className="text-muted">
            Bạn có chắc chắn muốn xóa tài khoản <br />
            <strong className="text-dark">{accountName}</strong> không? Hành
            động này không thể hoàn tác.
          </p>
        </div>
        <div
          style={{
            padding: "1rem",
            borderTop: "1px solid #dee2e6",
            display: "flex",
            justifyContent: "flex-end",
            gap: "0.5rem",
          }}
        >
          <button
            type="button"
            className="btn btn-light border"
            onClick={onClose}
            disabled={loading}
          >
            Hủy
          </button>
          <button
            type="button"
            className="btn btn-danger"
            onClick={onConfirm}
            disabled={loading}
          >
            {loading ? "Đang xóa..." : "Xác nhận xóa"}
          </button>
        </div>
      </div>
    </div>
  );
};

// === COMPONENT MODAL NHẬP OTP XÁC NHẬN XÓA ===
const ConfirmOtpModal = ({ isOpen, onClose, onSubmit, loading, error }) => {
  const [otp, setOtp] = useState("");
  if (!isOpen) return null;
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0,0,0,0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1050,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: 8,
          width: "90%",
          maxWidth: 400,
          padding: "1rem",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <h5 className="fw-bold">Nhập mã OTP</h5>
        <p className="text-muted">
          Vui lòng nhập mã OTP đã được gửi đến số điện thoại của bạn.
        </p>
        <input
          type="text"
          className="form-control mb-2"
          value={otp}
          onChange={(e) => setOtp(e.target.value)}
          placeholder="OTP code"
        />
        {error && <p className="text-danger small">{error}</p>}
        <div className="mt-3 d-flex justify-content-end gap-2">
          <button
            className="btn btn-secondary"
            onClick={onClose}
            disabled={loading}
          >
            Hủy
          </button>
          <button
            className="btn btn-primary"
            onClick={() => onSubmit(otp)}
            disabled={loading || !otp}
          >
            {loading ? "Đang xác nhận..." : "Xác nhận"}
          </button>
        </div>
      </div>
    </div>
  );
};

// === COMPONENT CHÍNH ===
const AcountBank = () => {
  const {
    data: listData,
    loading: listLoading,
    error: listError,
    callApi: getBankAccounts,
  } = useBankApi();
  const {
    // data: actionData, // không còn dùng trực tiếp
    loading: actionLoading,
    error: actionError,
    callApi: performBankAction,
  } = useBankApi();
  const { bankShortName } = useParams();
  const [accounts, setAccounts] = useState([]);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState(null);
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false);
  const [otpError, setOtpError] = useState("");

  const fetchAccounts = React.useCallback(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      getBankAccounts({ action: "bank_account", user_id: userId });
    }
  }, [getBankAccounts]);

  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  useEffect(() => {
    if (listData && listData.banks && Array.isArray(listData.banks)) {
      const filtered = listData.banks.filter(
        (acc) => acc.shortBankName === bankShortName
      );
      setAccounts(filtered);
    }
  }, [listData, bankShortName]);

  const handleStatusToggle = async (accountToToggle) => {
    const accountId = accountToToggle.id;
    const newStatus = accountToToggle.status === 1 ? 0 : 1;

    try {
      const userId = localStorage.getItem("user_id");
      await performBankAction({
        action: "change_status",
        user_id: userId,
        bankId: accountId,
        status: newStatus,
      });

      setAccounts((currentAccounts) =>
        currentAccounts.map((acc) =>
          acc.id === accountId
            ? {
                ...acc,
                status: newStatus,
                statusText:
                  newStatus === 1 ? "Đang hoạt động" : "Không hoạt động",
              }
            : acc
        )
      );
    } catch (error) {
      console.error("Failed to update status:", error);
      alert("Cập nhật trạng thái thất bại!");
    }
  };

  const handleDeleteClick = (account) => {
    console.log("🗑️ Delete button clicked for account:", account);
    setAccountToDelete(account);
    setIsConfirmModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!accountToDelete) return;

    const userId = localStorage.getItem("user_id");
    const bankId = accountToDelete.id;

    if (!userId || !bankId) {
      alert(
        "Lỗi: Thiếu User ID hoặc Bank ID. Vui lòng đăng nhập lại và thử lại."
      );
      setIsConfirmModalOpen(false);
      return;
    }

    // Gửi yêu cầu xóa để backend gửi OTP
    await performBankAction({
      action: "delete",
      user_id: userId,
      bankId: bankId,
    });

    setIsConfirmModalOpen(false);
    setIsOtpModalOpen(true);
  };

  const handleSubmitOtp = async (otp) => {
    setOtpError("");
    const userId = localStorage.getItem("user_id");
    const bankId = accountToDelete.id;
    try {
      await performBankAction({
        action: "confirm_otp_delete",
        user_id: userId,
        bankId: bankId,
        otp: otp,
        ip: "",
      });
      setIsOtpModalOpen(false);
      fetchAccounts();
    } catch (err) {
      console.error("OTP confirmation error:", err);
      setOtpError(err.message || "Xác nhận OTP thất bại");
    }
  };

  const formatCurrency = (amount) =>
    new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount || 0);
  useBootstrapTooltip([listLoading]);
  return (
    <MasterLayout>
      <Breadcrumb title={`Danh sách tài khoản ${bankShortName || ""}`} />
      <div className="container-fluid mt-4">
        {actionError && (
          <div className="alert alert-danger">
            Thao tác thất bại: {actionError}
          </div>
        )}

        <div className="card">
          <div className="card-body my-3">
            {listLoading ? (
              <p className="text-center p-5">Đang tải danh sách tài khoản...</p>
            ) : listError ? (
              <p className="text-center text-danger p-5">Lỗi: {listError}</p>
            ) : (
              <div className="table-responsive">
                <table className="table bordered-table mb-0">
                  <thead className="table-light">
                    <tr>
                      <th>ID</th>
                      <th>Tài khoản</th>
                      <th>
                        Số tài khoản ảo{" "}
                        <span
                          data-bs-toggle="tooltip"
                          data-bs-placement="top"
                          data-bs-title="Tài khoản ảo (Virtual Account - VA) thuộc tài khoản ngân hàng chính, hữu ích để phân biệt các khoản thu từ các nguồn khác nhau. Với ngân hàng kết nối qua OpenAPI, giao dịch phải qua VA thì Pay2S mới nhận được dữ liệu."
                        >
                          <Icon icon="ph:question" />
                        </span>
                      </th>
                      <th>Tổng tiền GD</th>
                      <th>Tổng số GD</th>
                      <th style={{ width: "150px", minWidth: "150px" }}>
                        Trạng thái
                      </th>
                      <th>Hoạt động</th>
                      <th>Hành động</th>
                      <th>Xoá</th>
                    </tr>
                  </thead>
                  <tbody>
                    {accounts.map((acc, index) => (
                      <tr key={acc.id}>
                        <td>{index + 1}</td>
                        <td>
                          <div className="fw-bold">{acc.name}</div>
                          <div className="text-muted">{acc.accountNumber}</div>
                          <div className="badge rounded-pill bg-success">
                            {acc.type}
                          </div>
                        </td>
                        <td>{acc.vaNumber || "-"}</td>
                        <td>{formatCurrency(acc.total_amount)}</td>
                        <td>{acc.transaction_count}</td>
                        <td>
                          <span
                            className={`w-100 badge ${
                              acc.status === 1 ? "bg-success" : "bg-secondary"
                            }`}
                          >
                            {acc.statusText}
                          </span>
                        </td>
                        <td className="text-center">
                          <label className="custom-switch">
                            <input
                              type="checkbox"
                              checked={acc.status === 1}
                              onChange={() => handleStatusToggle(acc)}
                              disabled={actionLoading}
                            />
                            <span className="slider round"></span>
                          </label>
                        </td>
                        <td>
                          {/* === FIX: THAY THẾ a -> Link và truyền STK === */}
                          <Link
                            to={`/client/banktrans?accountNumber=${acc.accountNumber}`}
                            className="btn btn-sm btn-success-focus text-white"
                          >
                            Lịch sử giao dịch
                          </Link>
                        </td>
                        <td>
                          <button
                            className="btn btn-sm btn-success"
                            onClick={() => handleDeleteClick(acc)}
                          >
                            <Icon icon="ph:trash" className="text-white" />
                          </button>
                        </td>
                      </tr>
                    ))}
                    {accounts.length === 0 && (
                      <tr>
                        <td colSpan="9" className="text-center p-4 text-muted">
                          Không tìm thấy tài khoản nào cho ngân hàng này.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      <ConfirmDeleteModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmDelete}
        accountName={accountToDelete?.accountNumber}
        loading={actionLoading}
      />
      <ConfirmOtpModal
        isOpen={isOtpModalOpen}
        onClose={() => setIsOtpModalOpen(false)}
        onSubmit={handleSubmitOtp}
        loading={actionLoading}
        error={otpError}
      />
    </MasterLayout>
  );
};

export default AcountBank;
