import { Icon } from "@iconify/react/dist/iconify.js";
import { Link, useNavigate } from "react-router-dom";
import { useEffect, useState, useRef } from "react";
import axios from "axios";
import logoImg from "../assets/images/logo.png";
import authImg from "../assets/images/auth/auth-img.png";

const SignInLayer = () => {
  const navigate = useNavigate();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [showOtp, setShowOtp] = useState(false);
  const [otpDigits, setOtpDigits] = useState(["", "", "", "", "", ""]);
  const [loginSession, setLoginSession] = useState({ token: "", user_id: "" });
  const otpRefs = useRef([]);

  useEffect(() => {
    const email = localStorage.getItem("remember_email");
    const pass = localStorage.getItem("remember_password");
    if (email && pass) {
      setUsername(email);
      setPassword(pass);
      setRememberMe(true);
    }
    const token = localStorage.getItem("token");
    if (token) navigate("/client/dashboard");
  }, [navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMsg("");
    setLoading(true);
    try {
      const loginRes = await axios.post(
        "https://api.pay2s.vn/api/v1/auth",
        new URLSearchParams({ action: "login", username, password }),
        { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
      );

      if (loginRes.data.status) {
        if (loginRes.data.otp === 1) {
          setShowOtp(true);
        } else {
          const { token, user_id } = loginRes.data;
          setLoginSession({ token, user_id });
          finishLogin(token, user_id);
        }
        setTimeout(() => otpRefs.current[0]?.focus(), 200);
      } else {
        setErrorMsg(loginRes.data.message || "Đăng nhập thất bại.");
      }
    } catch (err) {
      console.error(err);
      setErrorMsg("Lỗi hệ thống hoặc không thể kết nối máy chủ.");
    }
    setLoading(false);
  };

  const handleOtpChange = (index, value) => {
    if (/^\d?$/.test(value)) {
      const updated = [...otpDigits];
      updated[index] = value;
      setOtpDigits(updated);
      if (value && index < 5) otpRefs.current[index + 1]?.focus();
      else if (!value && index > 0) otpRefs.current[index - 1]?.focus();
    }
  };

  const handleOtpKeyDown = (e, index) => {
    if (e.key === "Backspace" && !otpDigits[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    } else if (e.key === "ArrowLeft" && index > 0) {
      otpRefs.current[index - 1]?.focus();
    } else if (e.key === "ArrowRight" && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }
  };

  const handleOtpPaste = (e) => {
    e.preventDefault();
    const paste = e.clipboardData
      .getData("text")
      .replace(/\D/g, "")
      .slice(0, 6);
    if (paste.length === 6) {
      const digits = paste.split("");
      setOtpDigits(digits);
      digits.forEach((digit, i) => {
        if (otpRefs.current[i]) otpRefs.current[i].value = digit;
      });
      otpRefs.current[5]?.focus();
    }
  };

  const handleConfirmOTP = async () => {
    const otp = otpDigits.join("");
    if (otp.length < 6) return setErrorMsg("Vui lòng nhập đủ 6 số OTP.");
    setLoading(true);
    setErrorMsg("");
    try {
      const res = await axios.post(
        "https://api.pay2s.vn/api/v1/auth",
        new URLSearchParams({ action: "confirmOTP", username, password, otp }),
        { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
      );

      if (res.data.status) {
        const { token, user_id } = res.data;
        finishLogin(token, user_id);
      } else {
        setErrorMsg(res.data.message || "Xác minh OTP thất bại.");
      }
    } catch (err) {
      console.error(err);
      setErrorMsg("Lỗi xác minh OTP.");
    }
    setLoading(false);
  };

  const finishLogin = (token, user_id) => {
    localStorage.setItem("token", token);
    localStorage.setItem("user_id", user_id);
    // Lưu thêm user name
    localStorage.setItem("username", username);
    if (rememberMe) {
      localStorage.setItem("remember_email", username);
      localStorage.setItem("remember_password", password);
    } else {
      localStorage.removeItem("remember_email");
      localStorage.removeItem("remember_password");
    }
    navigate("/client/dashboard");
  };

  return (
    <>
      <section className="auth bg-base d-flex flex-wrap">
        <div className="auth-left d-lg-block d-none">
          <div className="d-flex align-items-center flex-column h-100 justify-content-center">
            <img src={authImg} alt="Pay2S Login" />
          </div>
        </div>
        <div className="auth-right py-32 px-24 d-flex flex-column justify-content-center">
          <div className="max-w-464-px mx-auto w-100">
            <Link
              to="/client/login"
              className="mb-40 max-w-290-px d-inline-block"
            >
              <img src={logoImg} alt="Pay2S Logo" />
            </Link>
            <h4 className="mb-12">Đăng nhập</h4>
            <form onSubmit={handleSubmit}>
              <input
                type="text"
                className="form-control mb-3"
                placeholder="Username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
              />
              <input
                type="password"
                className="form-control mb-3"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              <div className="form-check mb-3">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="remember"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <label className="form-check-label" htmlFor="remember">
                  Ghi nhớ đăng nhập
                </label>
              </div>
              {errorMsg && !showOtp && (
                <div className="alert alert-danger">{errorMsg}</div>
              )}
              <button
                type="submit"
                className="btn btn-success w-100"
                disabled={loading}
              >
                {loading ? "Đang đăng nhập..." : "Đăng nhập"}
              </button>
              <div className="mt-3 text-center">
                <Link to="/client/forgot">Quên mật khẩu?</Link> |{" "}
                <Link to="/client/signup">Đăng ký</Link>
              </div>
            </form>
          </div>
        </div>
      </section>

      {showOtp && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0,0,0,0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 9999,
          }}
        >
          <div
            style={{
              background: "#fff",
              padding: 24,
              borderRadius: 12,
              width: "100%",
              maxWidth: 400,
            }}
          >
            <h5 className="mb-3 text-center">Xác minh OTP</h5>
            <div className="d-flex justify-content-between mb-3">
              {otpDigits.map((d, i) => (
                <input
                  key={i}
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  value={d}
                  onChange={(e) => handleOtpChange(i, e.target.value)}
                  onKeyDown={(e) => handleOtpKeyDown(e, i)}
                  onPaste={handleOtpPaste}
                  ref={(el) => (otpRefs.current[i] = el)}
                  maxLength={1}
                  className="otp-input-box text-center mx-1"
                />
              ))}
            </div>
            {errorMsg && (
              <div className="alert alert-danger py-2 text-center">
                {errorMsg}
              </div>
            )}
            <div className="d-flex justify-content-end gap-2">
              <button
                className="btn btn-secondary"
                onClick={() => setShowOtp(false)}
              >
                Hủy
              </button>
              <button
                className="btn btn-success"
                onClick={handleConfirmOTP}
                disabled={loading}
              >
                {loading ? "Đang xác minh..." : "Xác nhận"}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SignInLayer;
