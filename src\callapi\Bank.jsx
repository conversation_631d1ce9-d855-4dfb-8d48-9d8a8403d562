// src/callapi/Bank.jsx

import { useState, useCallback } from "react";
import axios from "axios";
import { API_BASE_URL } from "./apiConfig";

const useBankApi = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const callApi = useCallback(async (body) => {
    setLoading(true);
    setError(null);
    setData(null);

    const API_ENDPOINT = `${API_BASE_URL}/bank`;

    try {
      const token = localStorage.getItem("token");
      const headers = { "Content-Type": "application/x-www-form-urlencoded" };

      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      } else {
        throw new Error("Không tìm thấy token xác thực. Vui lòng đăng nhập.");
      }

      const requestBody = new URLSearchParams(body);

      const response = await axios.post(API_ENDPOINT, requestBody, { headers });
      const result = response.data;

      if (
        (body.action === "delete" && response.status === 200) ||
        result.status === true
      ) {
        setData(result);
        return result;
      } else {
        setError(result.message || "API trả về lỗi không xác định.");
        return null;
      }
    } catch (err) {
      let errorMessage = "Đã có lỗi xảy ra.";
      if (err.response) {
        errorMessage =
          err.response.data.message ||
          err.response.data.error ||
          "Lỗi từ máy chủ nhưng không có thông điệp cụ thể.";
      } else {
        errorMessage = err.message;
      }
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { data, loading, error, callApi };
};

export default useBankApi;
