import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Dropdown } from "bootstrap";
import { io } from "socket.io-client";
import useUserApi from "../../callapi/User";
import useSystemApi from "../../callapi/System";

const Notifications = () => {
  const [notifications, setNotifications] = useState([]);
  const socketRef = useRef(null);
  const dropdownToggleRef = useRef(null);
  const dropdownInstanceRef = useRef(null);

  const token = localStorage.getItem("token");
  const userId = localStorage.getItem("user_id");

  const {
    data: initialNotiData,
    loading: initialLoading,
    callApi: fetchInitialNotificationsApi,
  } = useSystemApi();
  const { callApi: markAsReadApi } = useUserApi();

  // fetch initial notifications
  useEffect(() => {
    if (userId) {
      fetchInitialNotificationsApi({ action: "user_noti", user_id: userId });
    }
  }, [userId, fetchInitialNotificationsApi]);

  // sort and set notifications
  useEffect(() => {
    if (initialNotiData?.status) {
      const sorted = (initialNotiData.notification || []).sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at)
      );
      setNotifications(sorted);
    }
  }, [initialNotiData]);

  // setup WebSocket
  useEffect(() => {
    if (!token || !userId) return;
    const socket = io("http://103.249.116.179:4004", {
      transports: ["websocket"],
    });
    socketRef.current = socket;
    socket.on("connect", () => {
      socket.emit("register", userId);
    });
    socket.on("new_notification", (newNoti) => {
      setNotifications((prev) => [newNoti, ...prev]);
      if (dropdownInstanceRef.current) dropdownInstanceRef.current.show();
    });
    return () => socket.disconnect();
  }, [token, userId]);

  // initialize Bootstrap dropdown
  useEffect(() => {
    if (dropdownToggleRef.current) {
      dropdownInstanceRef.current = new Dropdown(dropdownToggleRef.current);
    }
  }, []);

  const unreadCount = notifications.filter((n) => n.is_read === 0).length;

  const handleMarkAsRead = (notification) => {
    if (notification.is_read === 0) {
      setNotifications((prev) =>
        prev.map((n) => (n.id === notification.id ? { ...n, is_read: 1 } : n))
      );
      markAsReadApi({
        action: "mark_noti_read",
        user_id: userId,
        id: notification.id,
      });
    }
  };

  return (
    <>
      <button
        ref={dropdownToggleRef}
        className="has-indicator w-40-px h-40-px bg-neutral-200 rounded-circle d-flex justify-content-center align-items-center position-relative"
        type="button"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        data-bs-auto-close="outside"
      >
        <Icon icon="iconoir:bell" className="text-primary-light text-xl" />
        {unreadCount > 0 && (
          <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
            {unreadCount}
          </span>
        )}
      </button>

      <div className="dropdown-menu to-top dropdown-menu-lg p-0">
        <div className="m-16 py-12 px-16 radius-8 bg-primary-50 mb-16 d-flex align-items-center justify-content-between gap-2">
          <h6 className="text-lg text-primary-light fw-semibold mb-0">
            Thông báo
          </h6>
          {unreadCount > 0 && (
            <span className="text-primary-600 fw-semibold text-lg w-40-px h-40-px rounded-circle bg-base d-flex justify-content-center align-items-center">
              {unreadCount}
            </span>
          )}
        </div>
        <div className="max-h-400-px overflow-y-auto scroll-sm pe-4">
          {initialLoading ? (
            <div className="p-5 text-center">Đang tải...</div>
          ) : notifications.length > 0 ? (
            notifications.map((n) => (
              <Link
                key={n.id}
                to={n.link_to_screen || "#"}
                onClick={() => handleMarkAsRead(n)}
                className={`px-24 py-12 d-flex align-items-start gap-3 mb-2 justify-content-between notification-item ${
                  n.is_read === 0 ? "unread" : ""
                }`}
              >
                <div className="d-flex align-items-center gap-3">
                  <span
                    className={`w-44-px h-44-px rounded-circle d-flex justify-content-center align-items-center flex-shrink-0 ${
                      n.is_read === 0
                        ? "bg-success-subtle text-success-main"
                        : "bg-neutral-200 text-secondary-light"
                    }`}
                  >
                    <Icon icon="iconoir:bell" className="icon text-xxl" />
                  </span>
                  <div>
                    <h6
                      className={`text-md fw-semibold mb-4 ${
                        n.is_read === 1 ? "text-secondary-light" : ""
                      }`}
                    >
                      {n.title || "Thông báo hệ thống"}
                    </h6>
                    <p className="mb-0 text-sm text-secondary-light">
                      {n.content}
                    </p>
                    <span className="text-sm text-secondary-light flex-shrink-0">
                      {new Date(n.created_at || Date.now()).toLocaleString(
                        "vi-VN"
                      )}
                    </span>
                  </div>
                </div>
                {n.is_read === 0 && <div className="unread-indicator"></div>}
              </Link>
            ))
          ) : (
            <div className="p-3 text-center">Không có thông báo nào.</div>
          )}
        </div>
        <div className="text-center py-12 px-16">
          <Link
            to="https://pay2s.vn/blog/thong-bao/"
            className="text-primary-600 fw-semibold text-md"
            target="_blank"
            rel="noopener noreferrer"
          >
            Xem tất cả
          </Link>
        </div>
      </div>
    </>
  );
};

export default Notifications;
