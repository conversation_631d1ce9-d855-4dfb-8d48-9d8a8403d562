import { BrowserRouter, Route, Routes, useLocation } from "react-router-dom";
import "./assets/css/style.css";
import { useEffect } from "react";
import ProtectedRoute from "./router/ProtectedRoute";
import RouteScrollToTop from "./helper/RouteScrollToTop";
import { motion } from "framer-motion";
import useAffiliateApi from "./callapi/AffiliateApi";

// Client

import AffiliateUser from "./client/AffiliateUser";
import AffWithDraw from "./client/AffWithDraw";
import BankTrans from "./client/BankTrans";
import BankList from "./client/BankList";
import ClientLogin from "./client/ClientLogin";
import ClientSignup from "./client/ClientSignup";
import DashBoard from "./client/DashBoard";
import ForgotPass from "./client/ForgotPass";
import IntegrationPage from "./client/IntegrationPage";
import InvoiceUser from "./client/InvoiceUser";
import PriceTable from "./client/PriceTable";
import QrCreate from "./client/QrCreate";
import TelegramAdd from "./client/TelegramAdd";
import TelegramPage from "./client/TelegramPage";
import Transaction from "./client/Transaction";
import UserProfile from "./client/UserProfile";
import WebhookAdd from "./client/WebhookAdd";
import WebhookHistory from "./client/WebhookHistory";
import WebhookPage from "./client/WebhookPage";
import AcountBank from "./client/AcountBank";
import AddBank from "./client/AddBank";
import CheckOutPage from "./client/CheckOutPage";

// Component để theo dõi affiliate hit khi có ?aff=
const AffiliateHitTracker = () => {
  const location = useLocation();
  const { callApi } = useAffiliateApi();
  useEffect(() => {
    const aff = new URLSearchParams(location.search).get("aff");
    if (aff) {
      callApi({ action: "affiliatehit", user_id: aff });
    }
  }, [location.search, callApi]);
  return null;
};

function App() {
  return (
    <BrowserRouter>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        <RouteScrollToTop />
        <AffiliateHitTracker />

        <Routes>
          <Route path="/" element={<ClientLogin />} />
          <Route path="/client/login" element={<ClientLogin />} />
          <Route path="/client/signup" element={<ClientSignup />} />
          <Route path="/client/forgot" element={<ForgotPass />} />
          <Route element={<ProtectedRoute />}>
            <Route path="/" element={<ClientLogin />} />
            <Route path="/client/dashboard" element={<DashBoard />} />
            <Route path="/client/affiliate" element={<AffiliateUser />} />
            <Route path="/client/affwithdraw" element={<AffWithDraw />} />
            <Route path="/client/banktrans" element={<BankTrans />} />
            <Route path="/client/bank" element={<BankList />} />
            <Route path="/client/integration" element={<IntegrationPage />} />
            <Route path="/client/invoice" element={<InvoiceUser />} />
            <Route path="/client/pricetable" element={<PriceTable />} />
            <Route path="/client/qr" element={<QrCreate />} />
            <Route path="/client/telegram-add" element={<TelegramAdd />} />
            <Route path="/client/telegram" element={<TelegramPage />} />
            <Route path="/client/transaction" element={<Transaction />} />
            <Route path="/client/profile" element={<UserProfile />} />
            <Route path="/client/webhook-add" element={<WebhookAdd />} />
            <Route
              path="/client/webhook-history"
              element={<WebhookHistory />}
            />
            <Route path="/client/webhook" element={<WebhookPage />} />
            <Route path="/client/add-bank" element={<AddBank />} />
            <Route path="/client/checkout" element={<CheckOutPage />} />
            <Route
              path="/client/account-bank/:bankShortName"
              element={<AcountBank />}
            />
          </Route>
        </Routes>
      </motion.div>
    </BrowserRouter>
  );
}

export default App;
