import MasterLayout from "../masterLayout/MasterLayout";
import { Icon } from "@iconify/react";
import Breadcrumb from "../components/Breadcrumb";
import BlankPageLayer from "../components/BlankPageLayer";
import React, { useState, useMemo, useEffect } from "react";
import { bankLogos } from "../client/ImportImage";
import useWebhooksApi from "../callapi/Webhooks";

const asset = {
  ACB: { bg: bankLogos.logoAcb },
  BIDV: { bg: bankLogos.logoBidv },
  MBBank: { bg: bankLogos.logoMbb },
  // MoMo: { bg: bankLogos.logoMomo },
  SEAB: { bg: bankLogos.logoSeab },
  TCB: { bg: bankLogos.logoTcb },
  // TPB: { bg: bankLogos.logoTpb },
  VCB: { bg: bankLogos.logoVcb },
  VTB: { bg: bankLogos.logoVtb },
};
const WebhookPage = () => {
  // Hook to fetch webhooks
  const {
    data: apiRes,
    loading: hooksLoading,
    error: hooksError,
    callApi,
  } = useWebhooksApi();
  const [data, setData] = useState([]);
  const [accountFilter, setAccountFilter] = useState("");
  const [eventFilter, setEventFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selected, setSelected] = useState(null);
  const [showFullToken, setShowFullToken] = useState(false);

  // Fetch webhook list on mount
  useEffect(() => {
    callApi({ action: "list" });
  }, [callApi]);

  // Update data when API response arrives
  useEffect(() => {
    if (apiRes) {
      const items = apiRes.hooks || apiRes.data || [];
      const mapped = items.map((item) => {
        const match = item.bankName?.match(/\(([^)]+)\)/);
        const code = match ? match[1] : item.bankName;
        return {
          id: item.id,
          accountLogo: asset[code]?.bg || "",
          accountName: item.bankName,
          accountNumber: item.accountNumber,
          event: item.type,
          endpoint: item.webhook_url,
          addedAt: item.created_at,
          updatedAt: item.updated_at,
          active: item.status === "1",
          token: item.token,
        };
      });
      setData(mapped);
    }
  }, [apiRes]);

  // Filtered list
  const list = useMemo(() => {
    return data.filter((w) => {
      if (accountFilter && w.accountName !== accountFilter) return false;
      if (eventFilter && w.event !== eventFilter) return false;
      if (statusFilter) {
        const st = w.active ? "Hoạt động" : "Không hoạt động";
        if (st !== statusFilter) return false;
      }
      if (searchTerm) {
        const t = searchTerm.toLowerCase();
        if (!w.endpoint.toLowerCase().includes(t)) return false;
      }
      return true;
    });
  }, [data, accountFilter, eventFilter, statusFilter, searchTerm]);

  const toggleActive = (id) => {
    setData((d) =>
      d.map((w) => (w.id === id ? { ...w, active: !w.active } : w))
    );
  };

  const openToken = (w) => {
    setSelected(w);
    setShowFullToken(false);
    setShowModal(true);
  };

  const copyToken = () => {
    navigator.clipboard.writeText(selected.token || "");
  };

  return (
    <>
      {/* MasterLayout */}
      <MasterLayout>
        {/* Breadcrumb */}
        <Breadcrumb title="Danh sách Webhook" />
        {/* Add and filters toolbar */}
        <div className="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-2 mb-3">
          <div className="d-block d-md-flex w-100 gap-2">
            {/* filters toolbar */}
            <select
              className="form-select"
              value={accountFilter}
              onChange={(e) => setAccountFilter(e.target.value)}
            >
              <option value="">Tất cả tài khoản</option>
              {Array.from(new Set(data.map((w) => w.accountName))).map(
                (name) => (
                  <option key={name} value={name}>
                    {name}
                  </option>
                )
              )}
            </select>
            <select
              className="form-select mt-3 mt-md-0"
              value={eventFilter}
              onChange={(e) => setEventFilter(e.target.value)}
            >
              <option value="">Tất cả sự kiện</option>
              {Array.from(new Set(data.map((w) => w.event))).map((ev) => (
                <option key={ev} value={ev}>
                  {ev}
                </option>
              ))}
            </select>
            <select
              className="form-select mt-3 mt-md-0"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Hoạt động">Hoạt động</option>
              <option value="Không hoạt động">Không hoạt động</option>
            </select>
            <input
              type="search"
              className="form-control form-control-sm mt-3 mt-md-0"
              placeholder="Tìm kiếm..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="d-none d-md-flex gap-2 w-100 justify-content-md-end">
            <button className="btn btn-effect btn-sm">Excel</button>
            <button className="btn btn-effect btn-sm">Print</button>
            <button className="btn-effect btn btn-sm">
              <Icon className="mx-2" icon="solar:add-circle-outline" />
              Thêm webhook
            </button>
          </div>
        </div>
        <div className="card mb-4">
          <div className="card-body p-0">
            <div className="table-responsive">
              <table
                className="table bordered-table mb-0"
                style={{ tableLayout: "fixed", width: "100%" }}
              >
                <thead>
                  <tr className="text-center">
                    <th className="id">ID</th>
                    <th style={{ width: "150px" }}>Tài khoản</th>
                    <th className="text-center">Sự kiện</th>
                    <th
                      style={{
                        width: "350px",
                        overfolow: "hidden",
                        whiteSpace: "nowrap",
                        textOverflow: "ellipsis",
                      }}
                    >
                      Endpoint
                    </th>
                    <th>Thêm/Cập nhật</th>
                    <th className="text-center">Hoạt động</th>
                    <th>Token</th>
                    <th>Hành động</th>
                  </tr>
                </thead>
                <tbody>
                  {list.length > 0 ? (
                    list.map((w) => (
                      <tr key={w.id}>
                        <td>{w.id}</td>
                        <td className="text-center" style={{ width: "150px" }}>
                          <img
                            src={w.accountLogo}
                            alt={w.accountName}
                            width={80}
                            className="me-2"
                          />
                          <br />
                          {w.accountNumber}
                        </td>
                        <td className="text-center">
                          <button className="alert alert-primary p-4 small">
                            {w.event}
                          </button>
                        </td>
                        <td
                          style={{
                            width: "200px",
                            overflow: "hidden",
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                          }}
                        >
                          {w.endpoint}
                        </td>
                        <td>
                          <small>{w.addedAt}</small>
                          <hr />
                          <small>{w.updatedAt}</small>
                        </td>
                        <td className="text-center">
                          <label className="custom-switch">
                            <input
                              type="checkbox"
                              checked={w.active}
                              onChange={() => toggleActive(w.id)}
                            />
                            <span className="slider round"></span>
                          </label>
                        </td>
                        <td>
                          <Icon
                            icon="mdi:key"
                            role="button"
                            onClick={() => openToken(w)}
                          />
                        </td>
                        <td>{/* actions */}</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={9} className="text-center p-4">
                        Không có dữ liệu để hiển thị
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
          {/* Pagination */}
          <div className="card-footer d-flex justify-content-end">
            <nav>
              <ul className="pagination pagination-sm mb-0">
                <li className="page-item disabled">
                  <a className="page-link" href="#">
                    1
                  </a>
                </li>
                <li className="page-item disabled">
                  <a className="page-link" href="#">
                    7
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </MasterLayout>
      {/* Token Modal */}
      {showModal && selected && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="token-modal modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header bg-success text-white">
                  <h5 className="modal-title">
                    Token webhook tài khoản: {selected.accountNumber}
                  </h5>
                  <button
                    type="button"
                    className="btn-close btn-close-white"
                    onClick={() => setShowModal(false)}
                  ></button>
                </div>
                <div className="modal-body d-flex align-items-center gap-2">
                  <input
                    type={showFullToken ? "text" : "password"}
                    readOnly
                    className="form-control"
                    value={selected.token}
                  />
                  <Icon
                    icon={showFullToken ? "mdi:eye-off" : "mdi:eye"}
                    onClick={() => setShowFullToken(!showFullToken)}
                    role="button"
                  />
                  <button
                    className="btn btn-outline-success btn-sm"
                    onClick={copyToken}
                  >
                    <Icon icon="mdi:content-copy" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default WebhookPage;
